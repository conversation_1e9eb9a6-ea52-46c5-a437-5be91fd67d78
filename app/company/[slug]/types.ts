import { ReactNode } from "react";
import { Video } from "@/common/components/organisms/videoPlaylist/types";
import { KeyedMutator } from "swr";

export enum ShowcaseType {
  TEXT = "text",
  VIDEO = "video",
}

export type CompanyShowcaseProps = {
  params: {
    slug: string;
  };
};

export type AiPromptType = {
  id: number;
  sequence: number;
  question: string;
};

export type TitleSectionProps = {
  title: string;
  subtitle: string;
  updatedAt: string;

  watchlistCount: number;
  price: string;
  industries: Array<{
    id: number;
    name: string;
  }>;
  tags: {
    buy_back: boolean;
    company_size: CompanySize;
    debt: boolean;
    id: number;
    income: boolean;
    mag7: boolean;
    management_ownership_more_than_5_percent: boolean;
    region: Region;
    revenue_stage: RevenueStage;
    showcase_id: number;
    type_of_stock: TypeOfStock;
  };
  changePercent: string;
  graphLink: string;
  teaserVideoLink: string;
  teaserVideoLabel: string;
  handleAddToWatchlist: (source: string) => void;
  handleRateStock?: (source: string) => void;
  rateStockLabel?: () => string;
  showRateStockButton?: boolean;
  showWatchlistButton?: boolean;
  logo: string;
  heroCTALink: string;
  heroCTALabel: string;
  slug: string;
  discoveryCoverImage?: string;
  privateShowcase: boolean;
  companyName: string;
  enableCurationAI: boolean;
  teaserVideoTitle: string;
  teaserVideoCaption: string;
  exchange: string;
  symbol: string;
  isWatchlisted: boolean;
  setIsWatchlisted: (value: boolean) => void;
  primaryCTALabel: () => string;
  buy: Array<{
    title: string;
    caption: string;
  }>;
  sell: Array<{
    title: string;
    caption: string;
  }>;
  prompts: Array<AiPromptType>;
};

export type FAQType = {
  question: string;
  answer: string;
  video_iframe_url?: string;
};

export type USPType = {
  title: string;
  description: string;
  video_iframe_url?: string;
  video?: string;
};

export type CatalystTypes = "short" | "medium" | "long";

export type CatalystType = {
  type?: CatalystTypes;
  description: string;
  video_iframe_url?: string;
  video?: string;
  title?: string;
};

export type KeyRiskType = {
  title: string;
  description: string;
  video_iframe_url?: string;
  video?: string;
  id: number;
};

export type KeyInfoSectionProps = {
  data: {
    title: string;
    content: string;
    graphLink: string;
    investorRelationsLabel: string | null;
    investorRelationsLink: string;
    usps: USPType[];
    keyRisks: KeyRiskType[];
    catalysts: CatalystType[];
    ceoInterviews: Video[];
    showcaseId: number;
  };
  type: ShowcaseType;
  slug: string;
  privateShowcase?: boolean;
};

export type FAQSectionProps = {
  data: FAQType[];
  slug: string;
  privateShowcase?: boolean;
};

export type TeamMemberType = {
  id: number;
  name: string;
  role: string;
  image: string;
  linkedin: string;
  x_link: string;
  description: string;
};

export type TeamSectionProps = {
  team: TeamMemberType[];
  slug: string;
};

export type SocialsType = {
  id: number;
  name: string;
  social_link: string;
  image: string;
};

export type KeySocialsSectionProps = {
  socials: SocialsType[];
};

export type CompanyResearchSectionProps = {
  url: string;
};

export type LetsConnectSectionProps = {
  params: {
    slug: string;
  };
};

export type ArticleType = {
  id: number;
  title: string;
  description: string;
  date: string;
  link: string;
};

export type ArticlesSectionProps = {
  articles: ArticleType[];
  slug: string;
};

export type CompanyContentType = {
  id: number;
  title: string;
  description?: string;
  type: string;
  content: string;
  content_iframe_url: string;
};

export type ExternalInsightsType = {
  id: number;
  title: string;
  type: string;
  content: string;
  content_iframe_url: string;
};

export type CompanyContentSectionProps = {
  content: {
    theme: string;
    contents: CompanyContentType[];
  }[];
  slug: string;
};

export type ExternalInsightsSectionProps = {
  content: {
    theme: string;
    contents: ExternalInsightsType[];
  }[];
  slug: string;
};

export type HeaderProps = {
  slug: string;
  price: string;
  watchlistCount: number;
  isAssistantAvailable: boolean;
  setCopilotOpened: (value: boolean) => void;
  onMobileNavToggle?: (isOpen: boolean) => void;
};

export type TeaserVideoSectionProps = {
  videoLink: string;
};

export type SectionLayoutProps = {
  children: ReactNode;
  id?: string;
  className?: string;
};

export enum Alignment {
  LEFT = "text-left",
  CENTER = "text-center",
  RIGHT = "text-right",
}

export type SectionHeadingProps = {
  children: ReactNode;
  className?: string;
  color?: string;
  align?: Alignment;
  description?: string;
  descriptionColor?: string;
};

export enum CollectionTrackType {
  company = "company",
  external = "external",
}

export type CollectionTrackProps = {
  type: CollectionTrackType;
  collections: Array<{
    theme: string;
    contents: CompanyContentType[] | ExternalInsightsType[];
  }>;
  slug: string;
};

export enum ExpertQuoteType {
  TEXT = "text",
  VIDEO = "video",
  IMAGE = "image",
}

export enum ExpertQuotePlatformType {
  X = "x",
  TWITTER = "twitter",
  XTWITTER = "x/twitter",
  YOUTUBE = "youtube",
  LINKEDIN = "linkedin",
  ARTICLE = "article",
  PODCASTS = "podcasts",
  REDDIT = "reddit",
  SEEKING_ALPHA = "seeking_alpha",
}

export type ExpertQuote = {
  id: number;
  showcase_expert_id: number;
  platform: ExpertQuotePlatformType;
  type: ExpertQuoteType;
  content: string;
  cta_label: string;
  cta_link: string;
  title: string | null;
};

export type Expert = {
  name: string;
  image: string;
  expertise: string;
  audience: string;
  x_link: string;
  linkedin: string;
  website: string;
  quotes: ExpertQuote[];
};

export type ExpertsSectionProps = {
  experts: Expert[];
  slug: string;
};

export enum SocialIconType {
  X = "x",
  LINKEDIN = "linkedin",
  WEBSITE = "website",
}

export type ChatBotProps = {
  title: string;
  projectId: string;
  agentId: string;
  intent?: string;
  slug: string;
};

export type DarkBannerProps = {
  handleAddToWatchlist: (source: string) => void;
  privateShowcase: boolean;
  isWatchlisted: boolean;
  setIsWatchlisted: (value: boolean) => void;
  slug: string;
};

export type LightBannerProps = {
  handleAddToWatchlist: (source: string) => void;
  privateShowcase: boolean;
  isWatchlisted: boolean;
  setIsWatchlisted: (value: boolean) => void;
  slug: string;
};

export type PromptMessageProps = {
  message: string;
  thread_id?: string | undefined;
};

export type SendPromptProps = {
  slug: string;
  flagForRefetching: boolean;
  mutatePrompts: KeyedMutator<GetPromptsDTO>;
  setFlagForRefetching: (value: boolean) => void;
  setQuestionsExpanded: (value: boolean) => void;
  setEnableTypewriter: (value: boolean) => void;
  prompts: PromptDTO[];
};

export type SendPromptResponse = {
  data: {
    thread_id: string;
  };
};

export type PromptRoles = "user" | "assistant";

export interface GraphItem {
  [key: string]: number | string;
}

export type ParentChildrenGraph = Array<{
  [key: string]: Array<string> | ParentChildrenGraph;
}>;

export type PromptDTO = {
  content: string;
  id: string;
  role: PromptRoles;
  graph?: Array<GraphItem> | ParentChildrenGraph;
};

export type GetPromptsDTO = {
  data: Array<PromptDTO>;
};

export type GetPromptsProps = {
  slug: string;
  isNewSession: boolean;
  flagForRefetching: boolean;
};

export type HasSubscribedResponse = {
  data: {
    has_subscribed: boolean;
    added_to_showcase_hubspot: boolean;
  };
};

export type FollowShowcaseResponse = {
  data: {
    showcase_id: number;
    user_id: number;
  };
};

export type TickerDTO = {
  exchange: string;
  exchange_symbol: string;
  currency: string;
  currency_symbol: string;
};
export type GetTickersDTO = {
  data: Array<TickerDTO>;
  message: string;
};

export enum CompanySize {
  MICRO = "micro",
  SMALL = "small",
  MEDIUM = "medium",
  LARGE = "large",
  MEGA = "mega",
}

export const COMPANY_SIZE_MAP: Record<CompanySize, string> = {
  [CompanySize.MICRO]: "Micro",
  [CompanySize.SMALL]: "Small Cap",
  [CompanySize.MEDIUM]: "Medium Cap",
  [CompanySize.LARGE]: "Large Cap",
  [CompanySize.MEGA]: "Mega",
};

export enum Region {
  UK = "uk",
  EUROPE = "europe",
  CANADA = "canada",
  USA = "usa",
  ROW = "row",
}

export const REGION_MAP: Record<Region, string> = {
  [Region.UK]: "UK",
  [Region.EUROPE]: "Europe",
  [Region.CANADA]: "Canada",
  [Region.USA]: "USA",
  [Region.ROW]: "ROW",
};

export enum TypeOfStock {
  VALUE = "value",
  CYCLICAL = "cyclical",
  DEFENSIVE = "defensive",
  GROWTH = "growth",
}

export const TYPE_OF_STOCK_MAP: Record<TypeOfStock, string> = {
  [TypeOfStock.VALUE]: "Value",
  [TypeOfStock.CYCLICAL]: "Cyclical",
  [TypeOfStock.DEFENSIVE]: "Defensive",
  [TypeOfStock.GROWTH]: "Growth",
};

export enum RevenueStage {
  PRE_REVENUE = "pre_revenue",
  POST_REVENUE = "post_revenue",
  PROFITABLE = "profitable",
  NOT_PROFITABLE = "not_profitable",
}

export const REVENUE_STAGE_MAP: Record<RevenueStage, string> = {
  [RevenueStage.PRE_REVENUE]: "Pre-revenue",
  [RevenueStage.POST_REVENUE]: "Post-revenue",
  [RevenueStage.PROFITABLE]: "Profitable",
  [RevenueStage.NOT_PROFITABLE]: "Not profitable",
};
