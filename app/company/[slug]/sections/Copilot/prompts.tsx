import {
  useEffect,
  useRef,
} from "react"
import { Chat } from "./chat"
import { PromptsProps } from "./types"
import { PromptLoader } from "@/common/components/atoms"
import { Questions } from "./questions"

export const Prompts = ({
  prompts,
  slug,
  onSendPrompt,
  aiPrompts,
  questionsExpanded,
  copilotExpanded,
  isPrompting,
  isPromptsLoading,
  setEnableTypewriter,
  flagForRefetching,
  setNewPrompt,
  newPrompt,
  enableTypewriter,
  promptExpanded,
  toggleCustomEnable,
  isDesktopView,
}: PromptsProps) => {
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const promptsString = JSON.stringify(prompts)

  useEffect(() => {
    handleScrollToBottom()
  }, [promptsString, flagForRefetching])
  useEffect(() => {
    if (questionsExpanded) {
      handleScrollToBottom()
    }
  }, [questionsExpanded])

  const handleScrollToBottom = () => {
    if (chatContainerRef?.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }
  return (
    <div ref={chatContainerRef} className={`${copilotExpanded ? "flex-1" : "max-h-[320px]"} demo flex flex-col overflow-x-hidden overflow-y-auto scroll-smooth mb-3 pr-2`}>
      <div className="flex flex-col justify-end flex-1">
        <div className="flex flex-col gap-6 pr-0.5">
          {prompts.map((prompt) => (
            <Chat
              key={prompt.id}
              chatId={prompt.id}
              text={prompt.content}
              role={prompt.role}
              graph={prompt.graph || []}
              setEnableTypewriter={setEnableTypewriter}
              enableTypewriter={enableTypewriter}
              setNewPrompt={setNewPrompt}
              newPrompt={newPrompt}
              handleScrollToBottom={handleScrollToBottom}
            />
          ))}
        </div>
      </div>
      {(isPrompting || isPromptsLoading) && <PromptLoader word="THINKING" />}
      {!isDesktopView && (
        <div className="pl-1">
          <Questions
            aiPrompts={aiPrompts}
            slug={slug}
            flagForRefetching={flagForRefetching}
            questionsExpanded={questionsExpanded}
            onSendPrompt={onSendPrompt}
            promptExpanded={promptExpanded}
            toggleCustomEnable={() => { toggleCustomEnable() }}
          />
        </div>
      )}
    </div>
  )
}
