'use client';
import Link from "next/link";
import { HeaderProps } from "@/app/company/[slug]/types";
import {
  authToken,
} from "@/common/constants";
import { MenuIcon } from "@/common/components/icons/MenuIcon";
import { Popover } from "@headlessui/react";
import useSWRMutation from "swr/mutation";
import {
  mixpanelCustomEvent, MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import lang from '@/common/lang';
import {
  LogoIcon,
} from "@/common/components/icons";
import HeaderItems from "./HeaderItems";
import { routes } from "@/common/routes";
import { useEffect } from "react";
import { fetcher } from "@/common/utils/network/baseFetcher";
import {
  signOut,
  useSession,
} from "next-auth/react";
import {
  getLocalStorageItem,
  removeLocalStorageItem,
} from "@/common/utils/helpers";
import { logoutUrl } from "@/common/utils/network/endpoints";
import { httpRequestMethods } from "@/common/utils/network/constants";
import { homePageLink } from '../../../../common/constants';
import { User } from "lucide-react";

const { POST } = httpRequestMethods;

const {
  dashboard: {
    showcase: {
      header,
    },
  },
} = lang

const Header = ({
  slug, setCopilotOpened, watchlistCount, price, onMobileNavToggle,
}: HeaderProps) => {
  const { data: session } = useSession()

  const signOutOnBE = useSWRMutation(
    logoutUrl, fetcher);
  const { trigger } = signOutOnBE;

  useEffect(() => {
    sessionStorage.removeItem('redirect')
  }, [])

  const handleOpenCurationAI = () => {
    setCopilotOpened(true)
    mixpanelCustomEvent({
      eventName: MixpanelEventName.copilotExpanded,
      mixpanelProps: {
        page: 'Showcase',
        slug: slug,
      },
    })
  }

  const handleLogout = async () => {
    const token = getLocalStorageItem(authToken)
    if (token) {
      await trigger({
        method: POST,
      });
    }
    await signOut({ redirect: false });
    removeLocalStorageItem(authToken);
  };

  const handleLogin = () => {
    mixpanelCustomEvent({
      eventName: MixpanelEventName.showcaseInvestorLogin,
      mixpanelProps: {
        page: 'Showcase',
        slug: slug,
      },
    })
    sessionStorage.setItem('redirect', window.location.href)

    try {
      const currentWatchlist = JSON.parse(localStorage.getItem('watchlist') || '[]')
      if (!currentWatchlist.some((item: { slug: string }) => item.slug === slug)) {
        localStorage.setItem('watchlist', JSON.stringify([
          ...currentWatchlist,
          {
            slug: slug,
            title: '',
            logo: '',
            watchlistedPrice: price ? parseFloat(price) : 0,
            followedSince: new Date().toString(),
          },
        ]))
      }
    } catch (error) {
      console.error('Error adding to watchlist:', error)
    }
  }

  const handleSignUp = () => {
    mixpanelCustomEvent({
      eventName: MixpanelEventName.showcaseInvestorSignUp,
      mixpanelProps: {
        page: 'Showcase',
        slug: slug,
      },
    })
    sessionStorage.setItem('redirect', window.location.href)

    try {
      const currentWatchlist = JSON.parse(localStorage.getItem('watchlist') || '[]')
      if (!currentWatchlist.some((item: { slug: string }) => item.slug === slug)) {
        localStorage.setItem('watchlist', JSON.stringify([
          ...currentWatchlist,
          {
            slug: slug,
            title: '',
            logo: '',
            watchlistedPrice: price ? parseFloat(price) : 0,
            followedSince: new Date().toString(),
          },
        ]))
      }
    } catch (error) {
      console.error('Error adding to watchlist:', error)
    }
  }
  return (
    <div className={`flex justify-center fixed top-0 left-0 right-0 w-full mx-auto bg-[#0E0E0F] border-b-[1px] border-[#3F3D42] z-20`}>
      <div className="py-4 sm:px-8 px-4 w-full flex justify-between items-center relative">
        <div className="flex items-center justify-start gap-8">
          <Link href={homePageLink}>
            <LogoIcon />
          </Link>
          <div className="hidden xl:flex">
            <HeaderItems
              slug={slug}
              watchlistCount={watchlistCount}
              handleLogout={handleLogout}
              handleOpenCurationAI={handleOpenCurationAI}
            />
          </div>
        </div>
        <div className="flex items-center gap-2 lg:gap-4">
          {session?.user.role === "subscriber" ? (
            <Popover className="relative hidden xl:flex items-center justify-center">
              <Popover.Button className="bg-white w-8 h-8 rounded-full" data-cy="user-button">
                <span className="text-sm font-medium text-[#3F3D42] uppercase">{session.user?.firstName ? session.user.firstName.trim().charAt(0) : <span className="flex w-full h-full justify-center items-center"><User width={18} height={18} /></span>}</span>
              </Popover.Button>
              <Popover.Panel className="absolute top-[calc(100%+8px)] right-0 z-30 w-auto">
                {({ close }) => (
                  <div className="flex flex-col gap-1 bg-card-bg-secondary p-2 items-center rounded-lg">
                    {
                      <button data-cy="logout-button" onClick={() => {
                        handleLogout()
                        close()
                      }}
                      className='w-full'>
                        <div className="py-3 px-4 font-medium text-base text-center w-full leading-none text-header-link-item hover:bg-white hover:bg-opacity-10 rounded-md hover:text-white transition-all whitespace-nowrap">{header.logout}</div>
                      </button>
                    }
                  </div>
                )}
              </Popover.Panel>
            </Popover>
          ) : (
            <>
              <Link onClick={handleLogin} data-cy="login" href={`${routes.showcaseLogin}?showcase=${slug}`} className='w-full'>
                <div className="py-2.5 lg:py-[7px] px-4 font-medium text-sm lg:text-base text-center w-full leading-none text-header-link-item hover:bg-white hover:bg-opacity-10 rounded-md hover:text-white transition-all whitespace-nowrap">
                  {header.login}
                </div>
              </Link>
              <Link onClick={handleSignUp} data-cy="signup" href={`${routes.showcaseSignUp}?showcase=${slug}`} className='w-full'>
                <div className="py-2.5 lg:py-[7px] px-4 font-medium text-sm lg:text-base text-center w-full leading-none bg-white text-[#3F3D42] hover:bg-white/85 rounded-md  transition-all whitespace-nowrap">
                  {header.signup}
                </div>
              </Link>
            </>
          )}
          <div className="xl:hidden flex items-center gap-3">
            <Popover className="relative flex items-center justify-center">
              {({ open }) => {
                if (onMobileNavToggle) {
                  onMobileNavToggle(open);
                }

                return (
                  <>
                    <Popover.Button>
                      <MenuIcon />
                    </Popover.Button>
                    <Popover.Panel className="absolute top-[calc(100%+8px)] right-0 z-30 w-auto">
                      {({ close }) => (
                        <HeaderItems
                          watchlistCount={watchlistCount}
                          onClick={() => close()}
                          handleLogout={handleLogout}
                          handleOpenCurationAI={handleOpenCurationAI}
                          slug={slug}
                        />
                      )}
                    </Popover.Panel>
                  </>
                );
              }}
            </Popover>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Header;
