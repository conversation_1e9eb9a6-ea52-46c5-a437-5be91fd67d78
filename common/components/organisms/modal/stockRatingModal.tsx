'use client';

import {
  useState,
  useEffect,
} from 'react';
import { X } from 'lucide-react';
import { useSession } from 'next-auth/react';
import {
  mixpanelCustomEvent,
  MixpanelEventName,
} from '@/common/utils/mixpanel/eventTriggers';
import { ResponsivePie } from '@nivo/pie';
import {
  useStockRatingStats,
  RatingOption,
} from '@/common/hooks/useStockRatingStats';
import Spinner from '../../molecules/loader/spinner';
import { SignupLoginModal } from './signupLoginModal';

export interface StockRatingModalProps {
  isOpen: boolean;
  onClose: () => void;
  companyName: string;
  slug: string;
  source?: string;
}

export const StockRatingModal = ({
  isOpen,
  onClose,
  companyName,
  slug,
  source,
}: StockRatingModalProps) => {
  const {
    data: session, status,
  } = useSession();
  const [selectedRating, setSelectedRating] = useState<RatingOption | null>(null);
  const [hasUserVoted, setHasUserVoted] = useState<boolean>(false);
  const [previousVote, setPreviousVote] = useState<RatingOption | null>(null);
  const [showSignupModal, setShowSignupModal] = useState<boolean>(false);
  const [isFirstVoteEver, setIsFirstVoteEver] = useState<boolean>(false);
  const [hasCompletedAuth, setHasCompletedAuth] = useState<boolean>(false);
  const {
    stats, isLoading: isLoadingStats, updateOptimisticStats,
  } = useStockRatingStats(slug, isOpen);

  useEffect(() => {
    if (status === 'authenticated' && session) {
      setHasCompletedAuth(true);
    } else {
      setHasCompletedAuth(false);
    }
  }, [session, status]);

  useEffect(() => {
    const userVotes = localStorage.getItem('stockRatingVotes');
    if (userVotes) {
      try {
        const votes = JSON.parse(userVotes);
        if (votes[slug]) {
          setHasUserVoted(true);
          setPreviousVote(votes[slug].rating);
        }
        const hasAnyVotes = Object.keys(votes).length > 0;
        setIsFirstVoteEver(!hasAnyVotes);
      } catch (error) {
        setIsFirstVoteEver(true);
      }
    } else {
      setIsFirstVoteEver(true);
    }
  }, [slug]);

  const pieData = [
    {
      id: 'buy',
      label: 'Buy',
      value: stats.buy,
      color: 'hsl(262, 80%, 65%)',
    },
    {
      id: 'hold',
      label: 'Hold',
      value: stats.hold,
      color: 'hsl(262, 96%, 79%)',

    },
    {
      id: 'sell',
      label: 'Sell',
      value: stats.sell,
      color: 'hsl(262, 96%, 89%)',
    },
  ];

  const handleRatingSelect = (rating: RatingOption) => {
    setSelectedRating(rating);
  };

  const handleSubmit = () => {
    if (selectedRating) {
      const userVotes = localStorage.getItem('stockRatingVotes');
      let votes: Record<string, { rating: RatingOption; timestamp: string }> = {};
      if (userVotes) {
        try {
          votes = JSON.parse(userVotes);
        } catch (error) {
        }
      }

      updateOptimisticStats(selectedRating, previousVote || undefined);

      votes[slug] = {
        rating: selectedRating,
        timestamp: new Date().toISOString(),
      };
      localStorage.setItem('stockRatingVotes', JSON.stringify(votes));

      setPreviousVote(selectedRating);
      setHasUserVoted(true);

      mixpanelCustomEvent({
        eventName: MixpanelEventName.stockRatingSubmitted,
        mixpanelProps: {
          page: 'Showcase',
          slug: slug,
          source: source,
          companyName: companyName,
          rating: selectedRating,
        },
      });

      onClose();

      if (isFirstVoteEver) {
        setShowSignupModal(true);
      }

      setSelectedRating(null);
    }
  };

  const getRatingIcon = (rating: RatingOption) => {
    switch (rating) {
    case 'buy':
      return '💰';
    case 'hold':
      return '✋';
    case 'sell':
      return '💸';
    default:
      return '';
    }
  };

  const getRatingLabel = (rating: RatingOption) => {
    switch (rating) {
    case 'buy':
      return 'Buy';
    case 'hold':
      return 'Hold';
    case 'sell':
      return 'Sell';
    default:
      return '';
    }
  };

  return (
    <>
      {isOpen ? (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 sm:px-0 px-4 backdrop-blur-sm">
          <div className="bg-[#1D1C20] rounded-lg w-full max-w-2xl sm:h-auto sm:max-h-[90vh] overflow-auto sm:overflow-visible relative">
            <div className="py-6 px-6">
              <div className="flex justify-between items-center pb-4">
                <h2 className="text-white text-xl font-medium">{hasUserVoted && hasCompletedAuth ? "Investor Votes" : `Rate ${companyName}`}</h2>
                <button
                  onClick={onClose}
                  className="text-white"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="text-white mb-6">{hasUserVoted && hasCompletedAuth ? "Here's how investors like you voted" : "What's your next move?"}</div>

              {hasUserVoted && hasCompletedAuth && (
                <div className="mb-8">
                  {isLoadingStats ? (
                    <div className="flex items-center justify-center">
                      <Spinner />
                    </div>
                  ) : stats.total === 0 ? (
                    <div className="flex flex-col items-center justify-center h-48 text-center">
                      <div className="text-4xl mb-4">📊</div>
                      <div className="text-white font-medium mb-2">No votes yet</div>
                      <div className="text-[#BAB9BD] text-sm">Be the first to vote on {companyName}!</div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className="w-full h-72 mb-6">
                        <ResponsivePie
                          data={pieData}
                          margin={{
                            top: 60,
                            right: 40,
                            bottom: 60,
                            left: 40,
                          }}
                          innerRadius={0.3}
                          padAngle={2}
                          isInteractive={false}
                          cornerRadius={2}
                          activeOuterRadiusOffset={8}
                          colors={{ datum: 'data.color' }}
                          borderWidth={0}
                          enableArcLinkLabels={true}
                          arcLabelsSkipAngle={10}
                          arcLabelsTextColor="#ffffff"
                          arcLabelsRadiusOffset={0.6}
                          arcLabel={(d) => `${((d.value / stats.total) * 100).toFixed(0)}%`}
                          theme={{
                            labels: {
                              text: {
                                fontSize: 14,
                                textTransform: 'capitalize',
                                fill: '#cccccc',
                                fontWeight: 600,
                              },
                            },
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="flex gap-4 mb-6">
                {(['buy', 'hold', 'sell'] as RatingOption[]).map((rating) => {
                  const isSelected = selectedRating === rating;
                  const isPreviousVote = previousVote === rating && !isSelected;

                  return (
                    <button
                      key={rating}
                      onClick={() => handleRatingSelect(rating)}
                      className={`flex-1 p-4 rounded-lg transition-all duration-200 outline-none bg-[#272629] border shadow-sm hover:border-[#8E4FFB] ${isSelected
                        ? "border-[#8E4FFB]"
                        : isPreviousVote
                          ? "border-[#8E4FFB] opacity-80"
                          : "border-transparent"}`}
                    >
                      <div className="mb-2">{getRatingIcon(rating)}</div>
                      <div className="text-white font-medium">
                        {getRatingLabel(rating)}
                      </div>
                    </button>
                  );
                })}
              </div>

              <button
                onClick={handleSubmit}
                disabled={!selectedRating}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${selectedRating ? 'bg-white hover:bg-white/80 text-black' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
              >
                {hasUserVoted ? 'Update Vote' : 'Submit Vote'}
              </button>
            </div>
          </div>
        </div>
      ) : null}

      <SignupLoginModal
        isOpen={showSignupModal}
        onClose={() => {
          setShowSignupModal(false);
        }}
        companyName={companyName}
        slug={slug}
      />
    </>
  );
};
