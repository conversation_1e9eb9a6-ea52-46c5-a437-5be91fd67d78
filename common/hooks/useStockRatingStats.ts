import { 
  useState, useEffect, useCallback,
} from "react";

export type RatingOption = "buy" | "hold" | "sell";

export interface RatingStats {
  buy: number;
  hold: number;
  sell: number;
  total: number;
}

export interface RatingStatsData {
  stats: RatingStats;
  isLoading: boolean;
  error: string | null;
  updateOptimisticStats: (
    newRating: RatingOption,
    previousRating?: RatingOption
  ) => void;
}

export const useStockRatingStats = (slug: string, isOpen: boolean): RatingStatsData => {
  const [baseStats, setBaseStats] = useState<RatingStats>({
    buy: 0,
    hold: 0,
    sell: 0,
    total: 0,
  });
  const [optimisticUpdates, setOptimisticUpdates] = useState<RatingStats>({
    buy: 0,
    hold: 0,
    sell: 0,
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const stats = {
    buy: baseStats.buy + optimisticUpdates.buy,
    hold: baseStats.hold + optimisticUpdates.hold,
    sell: baseStats.sell + optimisticUpdates.sell,
    total: baseStats.total + optimisticUpdates.total,
  };

  const updateOptimisticStats = useCallback(
    (newRating: RatingOption, previousRating?: RatingOption) => {
      setOptimisticUpdates((current) => {
        const updates = { ...current };

        if (previousRating) {
          updates[previousRating] = Math.max(0, updates[previousRating] - 1);
          updates.total = Math.max(0, updates.total - 1);
        }

        updates[newRating] += 1;
        updates.total += 1;

        return updates;
      });
    },
    [],
  );

  useEffect(() => {
    const fetchRatingStats = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch("/api/mixpanel/rating-stats", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            slug: slug,
            from_date: "2024-01-01",
            to_date: new Date().toISOString().split("T")[0],
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to fetch rating stats from API");
        }

        const data = await response.json();
        setBaseStats(data.stats);
        setOptimisticUpdates({
          buy: 0,
          hold: 0,
          sell: 0,
          total: 0,
        });
      } catch (err) {
        console.error("Failed to fetch rating stats:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch rating stats",
        );

        setBaseStats({
          buy: 0,
          hold: 0,
          sell: 0,
          total: 0,
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (slug && isOpen) {
      fetchRatingStats();
    }
  }, [slug, isOpen]);

  return {
    stats,
    isLoading,
    error,
    updateOptimisticStats,
  };
};
